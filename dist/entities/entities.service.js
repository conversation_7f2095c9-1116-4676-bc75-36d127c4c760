"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EntitiesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const openai_service_1 = require("../openai/openai.service");
const activity_logger_service_1 = require("../common/activity-logger.service");
const validation_service_1 = require("../common/validation.service");
const slug_utils_1 = require("../utils/slug.utils");
const entityTypeSlugToDetailKey = (slug) => {
    const mapping = {
        'ai-tool': 'tool_details',
        'online-course': 'course_details',
        'agency': 'agency_details',
        'content-creator': 'content_creator_details',
        'community': 'community_details',
        'newsletter': 'newsletter_details',
        'dataset': 'dataset_details',
        'research-paper': 'research_paper_details',
        'software': 'software_details',
        'model': 'model_details',
        'project-reference': 'project_reference_details',
        'service-provider': 'service_provider_details',
        'investor': 'investor_details',
        'event': 'event_details',
        'job': 'job_details',
        'grant': 'grant_details',
        'bounty': 'bounty_details',
        'hardware': 'hardware_details',
        'news': 'news_details',
        'book': 'book_details',
        'podcast': 'podcast_details',
        'platform': 'platform_details',
    };
    return mapping[slug] || null;
};
const entityTypeSlugToPrismaDetailKey = (slug) => {
    const mapping = {
        'ai-tool': 'entityDetailsTool',
        'course': 'entityDetailsCourse',
        'online-course': 'entityDetailsCourse',
        'agency': 'entityDetailsAgency',
        'content-creator': 'entityDetailsContentCreator',
        'community': 'entityDetailsCommunity',
        'newsletter': 'entityDetailsNewsletter',
        'dataset': 'entityDetailsDataset',
        'research-paper': 'entityDetailsResearchPaper',
        'software': 'entityDetailsSoftware',
        'model': 'entityDetailsModel',
        'project-reference': 'entityDetailsProjectReference',
        'service-provider': 'entityDetailsServiceProvider',
        'investor': 'entityDetailsInvestor',
        'event': 'entityDetailsEvent',
        'job': 'entityDetailsJob',
        'grant': 'entityDetailsGrant',
        'bounty': 'entityDetailsBounty',
        'hardware': 'entityDetailsHardware',
        'news': 'entityDetailsNews',
        'book': 'entityDetailsBook',
        'podcast': 'entityDetailsPodcast',
        'platform': 'entityDetailsPlatform',
    };
    return mapping[slug] || null;
};
let EntitiesService = EntitiesService_1 = class EntitiesService {
    constructor(prisma, openaiService, activityLogger, validationService) {
        this.prisma = prisma;
        this.openaiService = openaiService;
        this.activityLogger = activityLogger;
        this.validationService = validationService;
        this.entityTypeMap = new Map();
        this.logger = new common_1.Logger(EntitiesService_1.name);
        console.log('----------------------------------------------------');
        console.log('[EntitiesService] CONSTRUCTOR CALLED');
        console.log('----------------------------------------------------');
    }
    async onModuleInit() {
        console.log('----------------------------------------------------');
        console.log('[EntitiesService] ON_MODULE_INIT CALLED. Attempting to load entity types...');
        console.log('----------------------------------------------------');
        await this.loadEntityTypes();
    }
    async loadEntityTypes() {
        console.log('[EntitiesService] loadEntityTypes - STARTING');
        try {
            const entityTypes = await this.prisma.entityType.findMany({
                select: { id: true, slug: true },
            });
            if (entityTypes && entityTypes.length > 0) {
                this.entityTypeMap.clear();
                entityTypes.forEach(type => this.entityTypeMap.set(type.id, type.slug));
                console.log(`[EntitiesService] Entity types loaded into map. Count: ${this.entityTypeMap.size}.`);
                if (this.entityTypeMap.size > 0) {
                    const firstKey = this.entityTypeMap.keys().next().value;
                    if (firstKey) {
                        console.log(`[EntitiesService] Sample map entry - ID: ${firstKey}, Slug: ${this.entityTypeMap.get(firstKey)}`);
                    }
                }
                console.log('[EntitiesService] loadEntityTypes - FINISHED SUCCESSFULLY');
            }
            else {
                console.log('[EntitiesService] No entity types found in the database to load into map.');
            }
        }
        catch (error) {
            console.error('[EntitiesService] loadEntityTypes - CRITICAL FAILURE:', error);
            this.logger.error('[EntitiesService] CRITICAL: Failed to load entity types on startup:', error.stack);
        }
    }
    async getUniqueSlug(name) {
        let slug = (0, slug_utils_1.generateSlug)(name);
        let count = 0;
        while (await this.prisma.entity.findUnique({ where: { slug } })) {
            count++;
            slug = `${(0, slug_utils_1.generateSlug)(name)}-${count}`;
        }
        return slug;
    }
    async generateFtsTextForEntity(entityId, tx) {
        const entityWithDetails = await tx.entity.findUniqueOrThrow({
            where: { id: entityId },
            include: {
                entityType: true,
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        let textToEmbed = `${entityWithDetails.name || ''}. ${entityWithDetails.shortDescription || ''}. ${entityWithDetails.description || ''}.`;
        if (entityWithDetails.entityCategories.length > 0) {
            textToEmbed += ` Categories: ${entityWithDetails.entityCategories
                .map(ec => ec.category.name)
                .join(', ')}.`;
        }
        if (entityWithDetails.entityTags.length > 0) {
            textToEmbed += ` Tags: ${entityWithDetails.entityTags
                .map(et => et.tag.name)
                .join(', ')}.`;
        }
        if (entityWithDetails.entityFeatures.length > 0) {
            textToEmbed += ` Features: ${entityWithDetails.entityFeatures
                .map(ef => ef.feature.name)
                .join(', ')}.`;
        }
        const details = entityWithDetails.entityDetailsTool ||
            entityWithDetails.entityDetailsCourse ||
            entityWithDetails.entityDetailsAgency ||
            entityWithDetails.entityDetailsContentCreator ||
            entityWithDetails.entityDetailsCommunity ||
            entityWithDetails.entityDetailsNewsletter ||
            entityWithDetails.entityDetailsDataset ||
            entityWithDetails.entityDetailsResearchPaper ||
            entityWithDetails.entityDetailsSoftware ||
            entityWithDetails.entityDetailsModel ||
            entityWithDetails.entityDetailsProjectReference ||
            entityWithDetails.entityDetailsServiceProvider ||
            entityWithDetails.entityDetailsInvestor ||
            entityWithDetails.entityDetailsEvent ||
            entityWithDetails.entityDetailsJob ||
            entityWithDetails.entityDetailsGrant ||
            entityWithDetails.entityDetailsBounty ||
            entityWithDetails.entityDetailsHardware ||
            entityWithDetails.entityDetailsNews ||
            entityWithDetails.entityDetailsBook ||
            entityWithDetails.entityDetailsPodcast ||
            entityWithDetails.entityDetailsPlatform;
        if (details) {
            if ('keyFeatures' in details && Array.isArray(details.keyFeatures)) {
                textToEmbed += ` Key Features: ${details.keyFeatures.join(', ')}.`;
            }
            if ('useCases' in details && Array.isArray(details.useCases)) {
                textToEmbed += ` Use Cases: ${details.useCases.join(', ')}.`;
            }
        }
        return textToEmbed;
    }
    async generateAndSaveEmbedding(entityId, tx) {
        this.logger.log(`[Embedding BG] Job Started for entity ${entityId}`);
        try {
            const textToEmbed = await this.generateFtsTextForEntity(entityId, tx);
            this.logger.log(`[Embedding BG] Text prepared for entity ${entityId}: "${textToEmbed.substring(0, 200)}..."`);
            this.logger.debug(`[Embedding BG] Full text for entity ${entityId}: ${textToEmbed}`);
            const embedding = await this.openaiService.generateEmbedding(textToEmbed);
            if (!embedding) {
                this.logger.warn(`[Embedding BG] OpenAI did not return an embedding for entity ${entityId}. Skipping DB update.`);
                return;
            }
            this.logger.log(`[Embedding BG] Embedding generated successfully for entity ${entityId}. Saving to DB...`);
            const vectorString = `[${embedding.join(',')}]`;
            await tx.$executeRaw `UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entityId}::uuid`;
            this.logger.log(`[Embedding BG] Successfully saved embedding for entity ${entityId}.`);
        }
        catch (error) {
            this.logger.error(`[Embedding BG] Job FAILED for entity ${entityId}:`, error.stack);
        }
    }
    mapToolDetailsToPrisma(toolDetails) {
        if (!toolDetails)
            return {};
        const prismaData = {};
        if (toolDetails.learning_curve !== undefined)
            prismaData.learningCurve = toolDetails.learning_curve;
        if (toolDetails.key_features !== undefined)
            prismaData.keyFeatures = toolDetails.key_features;
        if (toolDetails.programming_languages !== undefined)
            prismaData.programmingLanguages = toolDetails.programming_languages;
        if (toolDetails.frameworks !== undefined)
            prismaData.frameworks = toolDetails.frameworks;
        if (toolDetails.libraries !== undefined)
            prismaData.libraries = toolDetails.libraries;
        if (toolDetails.target_audience !== undefined)
            prismaData.targetAudience = toolDetails.target_audience;
        if (toolDetails.deployment_options !== undefined)
            prismaData.deploymentOptions = toolDetails.deployment_options;
        if (toolDetails.supported_os !== undefined)
            prismaData.supportedOs = toolDetails.supported_os;
        if (toolDetails.mobile_support !== undefined)
            prismaData.mobileSupport = toolDetails.mobile_support;
        if (toolDetails.api_access !== undefined)
            prismaData.apiAccess = toolDetails.api_access;
        if (toolDetails.customization_level !== undefined)
            prismaData.customizationLevel = toolDetails.customization_level;
        if (toolDetails.trial_available !== undefined)
            prismaData.trialAvailable = toolDetails.trial_available;
        if (toolDetails.demo_available !== undefined)
            prismaData.demoAvailable = toolDetails.demo_available;
        if (toolDetails.open_source !== undefined)
            prismaData.openSource = toolDetails.open_source;
        if (toolDetails.support_channels !== undefined)
            prismaData.supportChannels = toolDetails.support_channels;
        if (toolDetails.has_free_tier !== undefined)
            prismaData.hasFreeTier = toolDetails.has_free_tier;
        if (toolDetails.use_cases !== undefined)
            prismaData.useCases = toolDetails.use_cases;
        if (toolDetails.integrations !== undefined)
            prismaData.integrations = toolDetails.integrations;
        if (toolDetails.pricing_model !== undefined)
            prismaData.pricingModel = toolDetails.pricing_model;
        if (toolDetails.price_range !== undefined)
            prismaData.priceRange = toolDetails.price_range;
        if (toolDetails.pricing_details !== undefined)
            prismaData.pricingDetails = toolDetails.pricing_details;
        if (toolDetails.pricing_url !== undefined)
            prismaData.pricingUrl = toolDetails.pricing_url;
        if (toolDetails.support_email !== undefined)
            prismaData.supportEmail = toolDetails.support_email;
        if (toolDetails.has_live_chat !== undefined)
            prismaData.hasLiveChat = toolDetails.has_live_chat;
        if (toolDetails.community_url !== undefined)
            prismaData.communityUrl = toolDetails.community_url;
        return prismaData;
    }
    mapCourseDetailsToPrisma(courseDetails) {
        if (!courseDetails)
            return {};
        const prismaData = {};
        if (courseDetails.instructor_name !== undefined)
            prismaData.instructorName = courseDetails.instructor_name;
        if (courseDetails.duration_text !== undefined)
            prismaData.durationText = courseDetails.duration_text;
        if (courseDetails.skill_level !== undefined)
            prismaData.skillLevel = courseDetails.skill_level;
        if (courseDetails.prerequisites !== undefined)
            prismaData.prerequisites = courseDetails.prerequisites;
        if (courseDetails.syllabus_url !== undefined)
            prismaData.syllabusUrl = courseDetails.syllabus_url;
        if (courseDetails.enrollment_count !== undefined)
            prismaData.enrollmentCount = courseDetails.enrollment_count;
        if (courseDetails.certificate_available !== undefined)
            prismaData.certificateAvailable = courseDetails.certificate_available;
        return prismaData;
    }
    mapAgencyDetailsToPrisma(agencyDetails) {
        if (!agencyDetails)
            return {};
        const prismaData = {};
        if (agencyDetails.services_offered !== undefined)
            prismaData.servicesOffered = agencyDetails.services_offered;
        if (agencyDetails.industry_focus !== undefined)
            prismaData.industryFocus = agencyDetails.industry_focus;
        if (agencyDetails.target_client_size !== undefined)
            prismaData.targetClientSize = agencyDetails.target_client_size;
        if (agencyDetails.target_audience !== undefined)
            prismaData.targetAudience = agencyDetails.target_audience;
        if (agencyDetails.location_summary !== undefined)
            prismaData.locationSummary = agencyDetails.location_summary;
        if (agencyDetails.portfolio_url !== undefined)
            prismaData.portfolioUrl = agencyDetails.portfolio_url;
        if (agencyDetails.pricing_info !== undefined)
            prismaData.pricingInfo = agencyDetails.pricing_info;
        return prismaData;
    }
    mapContentCreatorDetailsToPrisma(contentCreatorDetails) {
        if (!contentCreatorDetails)
            return {};
        const prismaData = {};
        if (contentCreatorDetails.creator_name !== undefined)
            prismaData.creatorName = contentCreatorDetails.creator_name;
        if (contentCreatorDetails.primary_platform !== undefined)
            prismaData.primaryPlatform = contentCreatorDetails.primary_platform;
        if (contentCreatorDetails.focus_areas !== undefined)
            prismaData.focusAreas = contentCreatorDetails.focus_areas;
        if (contentCreatorDetails.follower_count !== undefined)
            prismaData.followerCount = contentCreatorDetails.follower_count;
        if (contentCreatorDetails.example_content_url !== undefined)
            prismaData.exampleContentUrl = contentCreatorDetails.example_content_url;
        return prismaData;
    }
    mapCommunityDetailsToPrisma(communityDetails) {
        if (!communityDetails)
            return {};
        const prismaData = {};
        if (communityDetails.platform !== undefined)
            prismaData.platform = communityDetails.platform;
        if (communityDetails.member_count !== undefined)
            prismaData.memberCount = communityDetails.member_count;
        if (communityDetails.focus_topics !== undefined)
            prismaData.focusTopics = communityDetails.focus_topics;
        if (communityDetails.rules_url !== undefined)
            prismaData.rulesUrl = communityDetails.rules_url;
        if (communityDetails.invite_url !== undefined)
            prismaData.inviteUrl = communityDetails.invite_url;
        if (communityDetails.main_channel_url !== undefined)
            prismaData.mainChannelUrl = communityDetails.main_channel_url;
        return prismaData;
    }
    mapNewsletterDetailsToPrisma(newsletterDetails) {
        if (!newsletterDetails)
            return {};
        const prismaData = {};
        if (newsletterDetails.frequency !== undefined)
            prismaData.frequency = newsletterDetails.frequency;
        if (newsletterDetails.main_topics !== undefined)
            prismaData.mainTopics = newsletterDetails.main_topics;
        if (newsletterDetails.archive_url !== undefined)
            prismaData.archiveUrl = newsletterDetails.archive_url;
        if (newsletterDetails.subscribe_url !== undefined)
            prismaData.subscribeUrl = newsletterDetails.subscribe_url;
        if (newsletterDetails.author_name !== undefined)
            prismaData.authorName = newsletterDetails.author_name;
        if (newsletterDetails.subscriber_count !== undefined)
            prismaData.subscriberCount = newsletterDetails.subscriber_count;
        return prismaData;
    }
    mapGenericDetailsToPrisma(detailsDto) {
        if (!detailsDto)
            return {};
        const prismaData = {};
        Object.keys(detailsDto).forEach(key => {
            if (detailsDto[key] !== undefined) {
                const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
                prismaData[camelKey] = detailsDto[key];
            }
        });
        return prismaData;
    }
    mapDetailsToPrisma(detailsDto, entityTypeSlug) {
        switch (entityTypeSlug) {
            case 'ai-tool':
                return this.mapToolDetailsToPrisma(detailsDto);
            case 'course':
            case 'online-course':
                return this.mapCourseDetailsToPrisma(detailsDto);
            case 'agency':
                return this.mapAgencyDetailsToPrisma(detailsDto);
            case 'content-creator':
                return this.mapContentCreatorDetailsToPrisma(detailsDto);
            case 'community':
                return this.mapCommunityDetailsToPrisma(detailsDto);
            case 'newsletter':
                return this.mapNewsletterDetailsToPrisma(detailsDto);
            case 'hardware':
            case 'software':
            case 'research-paper':
            case 'job':
            case 'event':
            case 'podcast':
            case 'grant':
            case 'dataset':
            case 'model':
            case 'project-reference':
            case 'service-provider':
            case 'investor':
            case 'bounty':
            case 'news':
            case 'book':
            case 'platform':
                return this.mapGenericDetailsToPrisma(detailsDto);
            default:
                return detailsDto || {};
        }
    }
    async create(createEntityDto, submitterUser) {
        await this.validationService.validateEntitySubmissionRate(submitterUser.id);
        const { entity_type_id, category_ids, tag_ids, feature_ids, name, website_url, short_description, description, logo_url, documentation_url, contact_url, privacy_policy_url, founded_year, social_links, meta_title, meta_description, employee_count_range, funding_stage, location_summary, ref_link, affiliate_status, scraped_review_sentiment_label, scraped_review_sentiment_score, scraped_review_count, tool_details, course_details, agency_details, content_creator_details, community_details, newsletter_details, dataset_details, research_paper_details, software_details, model_details, project_reference_details, service_provider_details, investor_details, event_details, job_details, grant_details, bounty_details, hardware_details, news_details, book_details, podcast_details, platform_details, } = createEntityDto;
        this.logger.log(`[EntitiesService Create] Received entity_type_id: ${entity_type_id}`);
        this.logger.log(`[EntitiesService Create] Current entityTypeMap (size ${this.entityTypeMap.size}): ${JSON.stringify(Array.from(this.entityTypeMap.entries()))}`);
        const entityTypeSlug = this.entityTypeMap.get(entity_type_id);
        if (!entityTypeSlug) {
            this.logger.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys()).join(', ')}]`);
            console.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys())}`);
            throw new common_1.BadRequestException(`Invalid entity_type_id: ${entity_type_id}`);
        }
        const providedDetails = {
            'ai-tool': tool_details,
            'course': course_details,
            'online-course': course_details,
            'agency': agency_details,
            'content-creator': content_creator_details,
            'community': community_details,
            'newsletter': newsletter_details,
            'dataset': dataset_details,
            'research-paper': research_paper_details,
            'software': software_details,
            'model': model_details,
            'project-reference': project_reference_details,
            'service-provider': service_provider_details,
            'investor': investor_details,
            'event': event_details,
            'job': job_details,
            'grant': grant_details,
            'bounty': bounty_details,
            'hardware': hardware_details,
            'news': news_details,
            'book': book_details,
            'podcast': podcast_details,
            'platform': platform_details,
        };
        let expectedDetailKeyForSlug = entityTypeSlugToDetailKey(entityTypeSlug);
        let detailIsProvided = false;
        if (tool_details)
            detailIsProvided = true;
        if (course_details)
            detailIsProvided = true;
        if (agency_details)
            detailIsProvided = true;
        if (content_creator_details)
            detailIsProvided = true;
        if (community_details)
            detailIsProvided = true;
        if (newsletter_details)
            detailIsProvided = true;
        if (dataset_details)
            detailIsProvided = true;
        if (research_paper_details)
            detailIsProvided = true;
        if (software_details)
            detailIsProvided = true;
        if (model_details)
            detailIsProvided = true;
        if (project_reference_details)
            detailIsProvided = true;
        if (service_provider_details)
            detailIsProvided = true;
        if (investor_details)
            detailIsProvided = true;
        if (event_details)
            detailIsProvided = true;
        if (job_details)
            detailIsProvided = true;
        if (grant_details)
            detailIsProvided = true;
        if (bounty_details)
            detailIsProvided = true;
        if (hardware_details)
            detailIsProvided = true;
        if (news_details)
            detailIsProvided = true;
        if (book_details)
            detailIsProvided = true;
        if (podcast_details)
            detailIsProvided = true;
        if (platform_details)
            detailIsProvided = true;
        let correctDetailIsPresent = false;
        if (entityTypeSlug === 'ai-tool' && tool_details)
            correctDetailIsPresent = true;
        else if ((entityTypeSlug === 'course' || entityTypeSlug === 'online-course') && course_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'agency' && agency_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'content-creator' && content_creator_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'community' && community_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'newsletter' && newsletter_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'dataset' && dataset_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'research-paper' && research_paper_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'software' && software_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'model' && model_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'project-reference' && project_reference_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'service-provider' && service_provider_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'investor' && investor_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'event' && event_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'job' && job_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'grant' && grant_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'bounty' && bounty_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'hardware' && hardware_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'news' && news_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'book' && book_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'podcast' && podcast_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'platform' && platform_details)
            correctDetailIsPresent = true;
        if (detailIsProvided && !correctDetailIsPresent) {
            throw new common_1.BadRequestException(`Incorrect detail DTO provided for entity type '${entityTypeSlug}'. Expected '${expectedDetailKeyForSlug || entityTypeSlug + '_details'}'.`);
        }
        const actualProvidedDetailObjects = [
            tool_details, course_details, agency_details,
            content_creator_details, community_details, newsletter_details,
            dataset_details, research_paper_details, software_details, model_details,
            project_reference_details, service_provider_details, investor_details,
            event_details, job_details, grant_details, bounty_details, hardware_details,
            news_details, book_details, podcast_details, platform_details,
        ].filter(detail => detail !== undefined);
        if (actualProvidedDetailObjects.length > 1) {
            throw new common_1.BadRequestException('Only one type of entity details can be provided alongside the main entity data.');
        }
        const slug = await this.getUniqueSlug(name);
        const directEntityData = {
            name,
            slug,
            websiteUrl: website_url,
            shortDescription: short_description,
            description,
            logoUrl: logo_url,
            documentationUrl: documentation_url,
            contactUrl: contact_url,
            privacyPolicyUrl: privacy_policy_url,
            foundedYear: founded_year,
            socialLinks: social_links,
            metaTitle: meta_title,
            metaDescription: meta_description,
            employeeCountRange: employee_count_range,
            fundingStage: funding_stage,
            locationSummary: location_summary,
            refLink: ref_link,
            affiliateStatus: affiliate_status,
            scrapedReviewSentimentLabel: scraped_review_sentiment_label,
            scrapedReviewSentimentScore: scraped_review_sentiment_score,
            scrapedReviewCount: scraped_review_count,
        };
        const newEntity = await this.prisma.$transaction(async (tx) => {
            const createData = {
                ...directEntityData,
                entityType: { connect: { id: entity_type_id } },
                submitter: { connect: { id: submitterUser.id } },
                status: prisma_1.EntityStatus.PENDING,
                ...(entityTypeSlug === 'ai-tool' && tool_details && { entityDetailsTool: { create: this.mapToolDetailsToPrisma(tool_details) } }),
                ...((entityTypeSlug === 'course' || entityTypeSlug === 'online-course') && course_details && { entityDetailsCourse: { create: this.mapCourseDetailsToPrisma(course_details) } }),
                ...(entityTypeSlug === 'agency' && agency_details && { entityDetailsAgency: { create: this.mapAgencyDetailsToPrisma(agency_details) } }),
                ...(entityTypeSlug === 'content-creator' && content_creator_details && { entityDetailsContentCreator: { create: this.mapContentCreatorDetailsToPrisma(content_creator_details) } }),
                ...(entityTypeSlug === 'community' && community_details && { entityDetailsCommunity: { create: this.mapCommunityDetailsToPrisma(community_details) } }),
                ...(entityTypeSlug === 'newsletter' && newsletter_details && { entityDetailsNewsletter: { create: this.mapNewsletterDetailsToPrisma(newsletter_details) } }),
                ...(entityTypeSlug === 'dataset' && dataset_details && { entityDetailsDataset: { create: this.mapDetailsToPrisma(dataset_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'research-paper' && research_paper_details && { entityDetailsResearchPaper: { create: this.mapDetailsToPrisma(research_paper_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'software' && software_details && { entityDetailsSoftware: { create: this.mapDetailsToPrisma(software_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'model' && model_details && { entityDetailsModel: { create: this.mapDetailsToPrisma(model_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'project-reference' && project_reference_details && { entityDetailsProjectReference: { create: this.mapDetailsToPrisma(project_reference_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'service-provider' && service_provider_details && { entityDetailsServiceProvider: { create: this.mapDetailsToPrisma(service_provider_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'investor' && investor_details && { entityDetailsInvestor: { create: this.mapDetailsToPrisma(investor_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'event' && event_details && { entityDetailsEvent: { create: this.mapDetailsToPrisma(event_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'job' && job_details && { entityDetailsJob: { create: this.mapDetailsToPrisma(job_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'grant' && grant_details && { entityDetailsGrant: { create: this.mapDetailsToPrisma(grant_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'bounty' && bounty_details && { entityDetailsBounty: { create: this.mapDetailsToPrisma(bounty_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'hardware' && hardware_details && { entityDetailsHardware: { create: this.mapDetailsToPrisma(hardware_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'news' && news_details && { entityDetailsNews: { create: this.mapDetailsToPrisma(news_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'book' && book_details && { entityDetailsBook: { create: this.mapDetailsToPrisma(book_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'podcast' && podcast_details && { entityDetailsPodcast: { create: this.mapDetailsToPrisma(podcast_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'platform' && platform_details && { entityDetailsPlatform: { create: this.mapDetailsToPrisma(platform_details, entityTypeSlug) } }),
                entityCategories: category_ids
                    ? { create: category_ids.map(catId => ({ categoryId: catId, assignedBy: submitterUser.id })) }
                    : undefined,
                entityTags: tag_ids
                    ? { create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: submitterUser.id })) }
                    : undefined,
                entityFeatures: feature_ids
                    ? { create: feature_ids.map(featId => ({ featureId: featId, assignedBy: submitterUser.id })) }
                    : undefined,
            };
            this.logger.debug(`[EntitiesService Create] createData object: ${JSON.stringify(createData, null, 2)}`);
            const newEntityTx = await tx.entity.create({
                data: createData,
                include: {
                    entityType: true,
                    submitter: {
                        select: {
                            id: true,
                            authUserId: true,
                            email: true,
                            createdAt: true,
                            lastLogin: true,
                            username: true,
                            displayName: true,
                            profilePictureUrl: true
                        }
                    },
                    entityCategories: { include: { category: true } },
                    entityTags: { include: { tag: true } },
                    entityFeatures: { include: { feature: true } },
                    entityDetailsTool: true,
                    entityDetailsCourse: true,
                    entityDetailsAgency: true,
                    entityDetailsContentCreator: true,
                    entityDetailsCommunity: true,
                    entityDetailsNewsletter: true,
                    entityDetailsDataset: true,
                    entityDetailsResearchPaper: true,
                    entityDetailsSoftware: true,
                    entityDetailsModel: true,
                    entityDetailsProjectReference: true,
                    entityDetailsServiceProvider: true,
                    entityDetailsInvestor: true,
                    entityDetailsEvent: true,
                    entityDetailsJob: true,
                    entityDetailsGrant: true,
                    entityDetailsBounty: true,
                    entityDetailsHardware: true,
                    entityDetailsNews: true,
                    entityDetailsBook: true,
                    entityDetailsPodcast: true,
                    entityDetailsPlatform: true,
                },
            });
            await tx.userSubmittedTool.create({
                data: {
                    userId: submitterUser.id,
                    entityId: newEntityTx.id,
                    submissionStatus: 'PENDING',
                },
            });
            await this.generateAndSaveEmbedding(newEntityTx.id, tx);
            return newEntityTx;
        });
        await this.activityLogger.logSubmissionActivity(submitterUser.id, newEntity.id, newEntity.name, newEntity.slug || newEntity.id, 'submitted');
        return newEntity;
    }
    async findAll(listEntitiesDto) {
        const { page = 1, limit = 10, status, entityTypeIds, categoryIds, tagIds, featureIds, searchTerm, sortBy = 'createdAt', sortOrder = 'desc', submitterId, createdAtFrom, createdAtTo, hasFreeTier, employeeCountRanges, fundingStages, locationSearch, apiAccess, pricingModels, priceRanges, integrations, platforms, targetAudience, entity_type_filters, rating_min, rating_max, review_count_min, review_count_max, affiliate_status, has_affiliate_link, } = listEntitiesDto;
        const skip = (page - 1) * limit;
        const where = {};
        const detailFilters = [];
        if (status)
            where.status = status;
        if (entityTypeIds?.length)
            where.entityTypeId = { in: entityTypeIds };
        if (submitterId)
            where.submitterId = submitterId;
        if (employeeCountRanges?.length)
            where.employeeCountRange = { in: employeeCountRanges };
        if (fundingStages?.length)
            where.fundingStage = { in: fundingStages };
        if (locationSearch)
            where.locationSummary = { contains: locationSearch, mode: 'insensitive' };
        if (affiliate_status)
            where.affiliateStatus = affiliate_status;
        if (has_affiliate_link !== undefined) {
            if (has_affiliate_link) {
                where.refLink = { not: null };
            }
            else {
                where.refLink = null;
            }
        }
        if (createdAtFrom || createdAtTo) {
            const createdAtCondition = {};
            if (createdAtFrom)
                createdAtCondition.gte = createdAtFrom;
            if (createdAtTo)
                createdAtCondition.lte = createdAtTo;
            where.createdAt = createdAtCondition;
        }
        if (categoryIds?.length)
            where.entityCategories = { some: { categoryId: { in: categoryIds } } };
        if (tagIds?.length)
            where.entityTags = { some: { tagId: { in: tagIds } } };
        if (featureIds?.length)
            where.entityFeatures = { some: { featureId: { in: featureIds } } };
        if (hasFreeTier !== undefined) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { hasFreeTier: hasFreeTier } },
                    { entityDetailsSoftware: { hasFreeTier: hasFreeTier } },
                    { entityDetailsPlatform: { hasFreeTier: hasFreeTier } },
                ],
            });
        }
        if (apiAccess !== undefined) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { apiAccess: apiAccess } },
                    { entityDetailsSoftware: { apiAccess: apiAccess } },
                    { entityDetailsPlatform: { apiAccess: apiAccess } },
                ],
            });
        }
        if (pricingModels && pricingModels.length > 0) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { pricingModel: { in: pricingModels } } },
                    { entityDetailsSoftware: { pricingModel: { in: pricingModels } } },
                    { entityDetailsPlatform: { pricingModel: { in: pricingModels } } },
                ],
            });
        }
        if (priceRanges && priceRanges.length > 0) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { priceRange: { in: priceRanges } } },
                    { entityDetailsSoftware: { priceRange: { in: priceRanges } } },
                    { entityDetailsPlatform: { priceRange: { in: priceRanges } } },
                ],
            });
        }
        if (integrations && integrations.length > 0) {
            const query = { array_contains: integrations };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { integrations: query } },
                    { entityDetailsSoftware: { integrations: query } },
                    { entityDetailsPlatform: { integrations: query } },
                ],
            });
        }
        if (platforms && platforms.length > 0) {
            const query = { array_contains: platforms };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { supportedOs: query } },
                    { entityDetailsSoftware: { supportedOs: query } },
                    { entityDetailsPlatform: { supportedOs: query } },
                ],
            });
        }
        if (targetAudience && targetAudience.length > 0) {
            const query = { array_contains: targetAudience };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { targetAudience: query } },
                    { entityDetailsSoftware: { targetAudience: query } },
                    { entityDetailsPlatform: { targetAudience: query } },
                ],
            });
        }
        if (rating_min !== undefined || rating_max !== undefined) {
            const ratingCondition = {};
            if (rating_min !== undefined)
                ratingCondition.gte = rating_min;
            if (rating_max !== undefined)
                ratingCondition.lte = rating_max;
            detailFilters.push({
                reviews: {
                    some: {
                        status: 'APPROVED',
                        rating: ratingCondition
                    }
                }
            });
        }
        if (review_count_min !== undefined || review_count_max !== undefined) {
            const reviewCountCondition = {};
            if (review_count_min !== undefined)
                reviewCountCondition.gte = review_count_min;
            if (review_count_max !== undefined)
                reviewCountCondition.lte = review_count_max;
            if (review_count_min !== undefined && review_count_min > 0) {
                detailFilters.push({
                    reviews: {
                        some: {
                            status: 'APPROVED'
                        }
                    }
                });
            }
        }
        if (entity_type_filters) {
            const entityTypeSpecificFilters = this.buildEntityTypeSpecificFilters(entity_type_filters);
            if (entityTypeSpecificFilters.length > 0) {
                detailFilters.push(...entityTypeSpecificFilters);
            }
        }
        if (detailFilters.length > 0) {
            where.AND = [...(where.AND || []), ...detailFilters];
        }
        if (searchTerm) {
            const ftsQuery = searchTerm.split(' ').filter(Boolean).join(' & ');
            const count = await this.prisma.entity.count({ where: { ...where, ftsDocument: { search: ftsQuery } } });
            const entities = await this.prisma.entity.findMany({
                where: { ...where, ftsDocument: { search: ftsQuery } },
                include: this.getFindAllIncludes(),
                orderBy: {
                    _relevance: {
                        fields: ['ftsDocument'],
                        search: ftsQuery,
                        sort: 'desc',
                    },
                },
                skip,
                take: limit,
            });
            return {
                data: entities,
                total: count,
                page,
                limit,
                totalPages: Math.ceil(count / limit),
            };
        }
        const total = await this.prisma.entity.count({ where });
        const orderBy = this.buildOrderBy(sortBy, sortOrder);
        const entities = await this.prisma.entity.findMany({
            where,
            include: this.getFindAllIncludes(),
            orderBy,
            skip,
            take: limit,
        });
        return {
            data: entities,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    getFindAllIncludes() {
        return {
            entityType: true,
            submitter: {
                select: {
                    id: true,
                    authUserId: true,
                    email: true,
                    createdAt: true,
                    lastLogin: true,
                    username: true,
                    displayName: true,
                    profilePictureUrl: true,
                },
            },
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
            reviews: {
                where: { status: prisma_1.ReviewStatus.APPROVED },
                select: { rating: true },
            },
            entityDetailsTool: true,
            entityDetailsCourse: true,
            entityDetailsAgency: true,
            entityDetailsContentCreator: true,
            entityDetailsCommunity: true,
            entityDetailsNewsletter: true,
            entityDetailsDataset: true,
            entityDetailsResearchPaper: true,
            entityDetailsSoftware: true,
            entityDetailsModel: true,
            entityDetailsProjectReference: true,
            entityDetailsServiceProvider: true,
            entityDetailsInvestor: true,
            entityDetailsEvent: true,
            entityDetailsJob: true,
            entityDetailsGrant: true,
            entityDetailsBounty: true,
            entityDetailsHardware: true,
            entityDetailsNews: true,
            entityDetailsBook: true,
            entityDetailsPodcast: true,
            entityDetailsPlatform: true,
            _count: {
                select: {
                    userSavedEntities: true,
                },
            },
        };
    }
    async findOne(id) {
        const entity = await this.prisma.entity.findUnique({
            where: { id },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true
                    }
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                reviews: {
                    where: { status: prisma_1.ReviewStatus.APPROVED },
                    orderBy: { createdAt: 'desc' },
                    include: {
                        user: { select: { id: true, username: true, profilePictureUrl: true } },
                        reviewVotes: true,
                    }
                },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        return entity;
    }
    async findBySlug(slug) {
        const entity = await this.prisma.entity.findUnique({
            where: { slug },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true
                    }
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                reviews: {
                    where: { status: prisma_1.ReviewStatus.APPROVED },
                    orderBy: { createdAt: 'desc' },
                    include: {
                        user: { select: { id: true, username: true, profilePictureUrl: true } },
                        reviewVotes: true,
                    }
                },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        return entity;
    }
    async update(id, updateEntityDto, currentUser) {
        const { name, website_url, short_description, description, logo_url, documentation_url, contact_url, privacy_policy_url, founded_year, social_links, category_ids, tag_ids, feature_ids, status, meta_title, meta_description, employee_count_range, funding_stage, location_summary, ref_link, affiliate_status, scraped_review_sentiment_label, scraped_review_sentiment_score, scraped_review_count, tool_details, course_details, agency_details, content_creator_details, community_details, newsletter_details, dataset_details, research_paper_details, software_details, model_details, project_reference_details, service_provider_details, investor_details, event_details, job_details, grant_details, bounty_details, hardware_details, news_details, book_details, podcast_details, platform_details, } = updateEntityDto;
        const entity = await this.prisma.entity.findUnique({
            where: { id },
            include: {
                submitter: true,
                entityCategories: true,
                entityTags: true,
                entityFeatures: true,
                entityType: true,
            },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        const canUpdate = entity.submitterId === currentUser.id ||
            currentUser.role === prisma_1.UserRole.ADMIN ||
            currentUser.role === prisma_1.UserRole.MODERATOR;
        if (!canUpdate) {
            throw new common_1.ForbiddenException('You do not have permission to update this entity.');
        }
        const updateData = {};
        if (name !== undefined) {
            updateData.name = name;
            updateData.slug = await this.getUniqueSlug(name);
        }
        if (website_url !== undefined)
            updateData.websiteUrl = website_url;
        if (short_description !== undefined)
            updateData.shortDescription = short_description;
        if (description !== undefined)
            updateData.description = description;
        if (logo_url !== undefined)
            updateData.logoUrl = logo_url;
        if (documentation_url !== undefined)
            updateData.documentationUrl = documentation_url;
        if (contact_url !== undefined)
            updateData.contactUrl = contact_url;
        if (privacy_policy_url !== undefined)
            updateData.privacyPolicyUrl = privacy_policy_url;
        if (founded_year !== undefined)
            updateData.foundedYear = founded_year;
        if (social_links !== undefined)
            updateData.socialLinks = social_links;
        if (meta_title !== undefined)
            updateData.metaTitle = meta_title;
        if (meta_description !== undefined)
            updateData.metaDescription = meta_description;
        if (employee_count_range !== undefined)
            updateData.employeeCountRange = employee_count_range;
        if (funding_stage !== undefined)
            updateData.fundingStage = funding_stage;
        if (location_summary !== undefined)
            updateData.locationSummary = location_summary;
        if (ref_link !== undefined)
            updateData.refLink = ref_link;
        if (affiliate_status !== undefined)
            updateData.affiliateStatus = affiliate_status;
        if (scraped_review_sentiment_label !== undefined)
            updateData.scrapedReviewSentimentLabel = scraped_review_sentiment_label;
        if (scraped_review_sentiment_score !== undefined)
            updateData.scrapedReviewSentimentScore = scraped_review_sentiment_score;
        if (scraped_review_count !== undefined)
            updateData.scrapedReviewCount = scraped_review_count;
        if (status !== undefined) {
            if (currentUser.role === prisma_1.UserRole.ADMIN ||
                currentUser.role === prisma_1.UserRole.MODERATOR) {
                updateData.status = status;
            }
            else if (status === prisma_1.EntityStatus.PENDING && entity.status === prisma_1.EntityStatus.NEEDS_REVISION) {
                updateData.status = prisma_1.EntityStatus.PENDING;
            }
            else if (status !== entity.status) {
                this.logger.warn(`User ${currentUser.id} attempted to change status of entity ${id} from ${entity.status} to ${status} without ADMIN/MODERATOR role.`);
            }
        }
        if (category_ids !== undefined) {
            updateData.entityCategories = {
                deleteMany: {},
                create: category_ids.map(catId => ({ categoryId: catId, assignedBy: currentUser.id })),
            };
        }
        if (tag_ids !== undefined) {
            updateData.entityTags = {
                deleteMany: {},
                create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: currentUser.id })),
            };
        }
        if (feature_ids !== undefined) {
            updateData.entityFeatures = {
                deleteMany: {},
                create: feature_ids.map(featId => ({ featureId: featId, assignedBy: currentUser.id })),
            };
        }
        const entityTypeSlug = entity.entityType.slug;
        if (entityTypeSlug === 'ai-tool' && tool_details) {
            const detailDataForPrisma = this.mapToolDetailsToPrisma(tool_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsTool = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'online-course' && course_details) {
            const detailDataForPrisma = this.mapCourseDetailsToPrisma(course_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsCourse = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'agency' && agency_details) {
            const detailDataForPrisma = this.mapAgencyDetailsToPrisma(agency_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsAgency = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'content-creator' && content_creator_details) {
            const detailDataForPrisma = this.mapContentCreatorDetailsToPrisma(content_creator_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsContentCreator = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'community' && community_details) {
            const detailDataForPrisma = this.mapCommunityDetailsToPrisma(community_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsCommunity = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'newsletter' && newsletter_details) {
            const detailDataForPrisma = this.mapNewsletterDetailsToPrisma(newsletter_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsNewsletter = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'dataset' && dataset_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(dataset_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsDataset = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'research-paper' && research_paper_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(research_paper_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsResearchPaper = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'software' && software_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(software_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsSoftware = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'model' && model_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(model_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsModel = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'project-reference' && project_reference_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(project_reference_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsProjectReference = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'service-provider' && service_provider_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(service_provider_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsServiceProvider = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'investor' && investor_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(investor_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsInvestor = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'event' && event_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(event_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsEvent = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'job' && job_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(job_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsJob = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'grant' && grant_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(grant_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsGrant = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'bounty' && bounty_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(bounty_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsBounty = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'hardware' && hardware_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(hardware_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsHardware = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'news' && news_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(news_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsNews = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'book' && book_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(book_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsBook = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'podcast' && podcast_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(podcast_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsPodcast = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'platform' && platform_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(platform_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsPlatform = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        const updatedEntity = await this.prisma.$transaction(async (tx) => {
            const updatedEntityTx = await tx.entity.update({
                where: { id },
                data: updateData,
                include: {
                    entityType: true,
                    submitter: {
                        select: {
                            id: true,
                            authUserId: true,
                            email: true,
                            createdAt: true,
                            lastLogin: true,
                            username: true,
                            displayName: true,
                            profilePictureUrl: true,
                        },
                    },
                    entityCategories: { include: { category: true } },
                    entityTags: { include: { tag: true } },
                    entityFeatures: { include: { feature: true } },
                    entityDetailsTool: true,
                    entityDetailsCourse: true,
                    entityDetailsAgency: true,
                    entityDetailsContentCreator: true,
                    entityDetailsCommunity: true,
                    entityDetailsNewsletter: true,
                    entityDetailsDataset: true,
                    entityDetailsResearchPaper: true,
                    entityDetailsSoftware: true,
                    entityDetailsModel: true,
                    entityDetailsProjectReference: true,
                    entityDetailsServiceProvider: true,
                    entityDetailsInvestor: true,
                    entityDetailsEvent: true,
                    entityDetailsJob: true,
                    entityDetailsGrant: true,
                    entityDetailsBounty: true,
                    entityDetailsHardware: true,
                    entityDetailsNews: true,
                    entityDetailsBook: true,
                    entityDetailsPodcast: true,
                    entityDetailsPlatform: true,
                },
            });
            await this.generateAndSaveEmbedding(updatedEntityTx.id, tx);
            return updatedEntityTx;
        });
        const entityForResponse = await this.prisma.entity.findUniqueOrThrow({
            where: { id: updatedEntity.id },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true,
                    },
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        delete entityForResponse.vector_embedding;
        return entityForResponse;
    }
    async adminSetStatus(id, newStatus) {
        const entityExists = await this.prisma.entity.findUnique({
            where: { id },
        });
        if (!entityExists) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        this.logger.log(`[EntitiesService AdminSetStatus] Admin changing status of entity ${id} to ${newStatus}`);
        return this.prisma.entity.update({
            where: { id },
            data: {
                status: newStatus,
            },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true
                    }
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
            },
        });
    }
    async remove(id, user) {
        this.logger.log(`[EntitiesService] User ${user.id} requested to remove entity ${id}`);
        const entity = await this.prisma.entity.findUnique({
            where: { id },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        const canDelete = entity.submitterId === user.id ||
            user.role === prisma_1.UserRole.ADMIN ||
            user.role === prisma_1.UserRole.MODERATOR;
        if (!canDelete) {
            throw new common_1.ForbiddenException('You do not have permission to delete this entity.');
        }
        await this.prisma.entity.delete({ where: { id } });
    }
    async vectorSearch(vectorSearchDto) {
        const { query, limit } = vectorSearchDto;
        const match_threshold = 0.5;
        const embedding = await this.openaiService.generateEmbedding(query);
        if (!embedding) {
            this.logger.warn(`Could not generate embedding for query: "${query}"`);
            return [];
        }
        const vectorString = `[${embedding.join(',')}]`;
        try {
            const results = await this.prisma.$queryRaw `
        SELECT
          id,
          name,
          "short_description" as "shortDescription",
          "logo_url" as "logoUrl",
          (
            SELECT slug
            FROM "public"."entity_types"
            WHERE id = "entity_type_id"
          ) as "entityTypeSlug",
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) as similarity
        FROM
          "public"."entities"
        WHERE
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) > ${match_threshold}
        ORDER BY
          similarity DESC
        LIMIT ${limit};
      `;
            return results;
        }
        catch (error) {
            this.logger.error('Vector search failed', error.stack);
            throw new common_1.InternalServerErrorException('An error occurred during vector search.');
        }
    }
    buildEntityTypeSpecificFilters(entityTypeFilters) {
        const filters = [];
        if (entityTypeFilters.course) {
            const courseFilter = this.buildCourseFilters(entityTypeFilters.course);
            if (courseFilter)
                filters.push(courseFilter);
        }
        if (entityTypeFilters.job) {
            const jobFilter = this.buildJobFilters(entityTypeFilters.job);
            if (jobFilter)
                filters.push(jobFilter);
        }
        if (entityTypeFilters.hardware) {
            const hardwareFilter = this.buildHardwareFilters(entityTypeFilters.hardware);
            if (hardwareFilter)
                filters.push(hardwareFilter);
        }
        if (entityTypeFilters.event) {
            const eventFilter = this.buildEventFilters(entityTypeFilters.event);
            if (eventFilter)
                filters.push(eventFilter);
        }
        if (entityTypeFilters.tool) {
            const toolFilter = this.buildToolFilters(entityTypeFilters.tool);
            if (toolFilter)
                filters.push(toolFilter);
        }
        if (entityTypeFilters.agency) {
            const agencyFilter = this.buildAgencyFilters(entityTypeFilters.agency);
            if (agencyFilter)
                filters.push(agencyFilter);
        }
        if (entityTypeFilters.software) {
            const softwareFilter = this.buildSoftwareFilters(entityTypeFilters.software);
            if (softwareFilter)
                filters.push(softwareFilter);
        }
        if (entityTypeFilters.research_paper) {
            const researchPaperFilter = this.buildResearchPaperFilters(entityTypeFilters.research_paper);
            if (researchPaperFilter)
                filters.push(researchPaperFilter);
        }
        if (entityTypeFilters.podcast) {
            const podcastFilter = this.buildPodcastFilters(entityTypeFilters.podcast);
            if (podcastFilter)
                filters.push(podcastFilter);
        }
        if (entityTypeFilters.community) {
            const communityFilter = this.buildCommunityFilters(entityTypeFilters.community);
            if (communityFilter)
                filters.push(communityFilter);
        }
        if (entityTypeFilters.grant) {
            const grantFilter = this.buildGrantFilters(entityTypeFilters.grant);
            if (grantFilter)
                filters.push(grantFilter);
        }
        if (entityTypeFilters.newsletter) {
            const newsletterFilter = this.buildNewsletterFilters(entityTypeFilters.newsletter);
            if (newsletterFilter)
                filters.push(newsletterFilter);
        }
        if (entityTypeFilters.book) {
            const bookFilter = this.buildBookFilters(entityTypeFilters.book);
            if (bookFilter)
                filters.push(bookFilter);
        }
        return filters;
    }
    buildCourseFilters(courseFilters) {
        const conditions = [];
        if (courseFilters.skill_levels?.length) {
            conditions.push({
                entityDetailsCourse: {
                    skillLevel: { in: courseFilters.skill_levels }
                }
            });
        }
        if (courseFilters.certificate_available !== undefined) {
            conditions.push({
                entityDetailsCourse: {
                    certificateAvailable: courseFilters.certificate_available
                }
            });
        }
        if (courseFilters.instructor_name) {
            conditions.push({
                entityDetailsCourse: {
                    instructorName: { contains: courseFilters.instructor_name, mode: 'insensitive' }
                }
            });
        }
        if (courseFilters.duration_text) {
            conditions.push({
                entityDetailsCourse: {
                    durationText: { contains: courseFilters.duration_text, mode: 'insensitive' }
                }
            });
        }
        if (courseFilters.enrollment_min !== undefined || courseFilters.enrollment_max !== undefined) {
            const enrollmentFilter = {};
            if (courseFilters.enrollment_min !== undefined)
                enrollmentFilter.gte = courseFilters.enrollment_min;
            if (courseFilters.enrollment_max !== undefined)
                enrollmentFilter.lte = courseFilters.enrollment_max;
            conditions.push({
                entityDetailsCourse: {
                    enrollmentCount: enrollmentFilter
                }
            });
        }
        if (courseFilters.prerequisites) {
            conditions.push({
                entityDetailsCourse: {
                    prerequisites: { contains: courseFilters.prerequisites, mode: 'insensitive' }
                }
            });
        }
        if (courseFilters.has_syllabus !== undefined) {
            if (courseFilters.has_syllabus) {
                conditions.push({
                    entityDetailsCourse: {
                        syllabusUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsCourse: {
                        syllabusUrl: null
                    }
                });
            }
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildJobFilters(jobFilters) {
        const conditions = [];
        if (jobFilters.employment_types?.length) {
            conditions.push({
                entityDetailsJob: {
                    jobType: { in: jobFilters.employment_types }
                }
            });
        }
        if (jobFilters.experience_levels?.length) {
            conditions.push({
                entityDetailsJob: {
                    experienceLevel: { in: jobFilters.experience_levels }
                }
            });
        }
        if (jobFilters.location_types?.length) {
            const locationConditions = jobFilters.location_types.map((locType) => ({
                entityDetailsJob: {
                    location: { contains: locType, mode: 'insensitive' }
                }
            }));
            conditions.push({ OR: locationConditions });
        }
        if (jobFilters.company_name) {
            conditions.push({
                entityDetailsJob: {
                    companyName: { contains: jobFilters.company_name, mode: 'insensitive' }
                }
            });
        }
        if (jobFilters.job_title) {
            conditions.push({
                entityDetailsJob: {
                    jobTitle: { contains: jobFilters.job_title, mode: 'insensitive' }
                }
            });
        }
        if (jobFilters.salary_min !== undefined || jobFilters.salary_max !== undefined) {
            const salaryFilter = {};
            if (jobFilters.salary_min !== undefined) {
                salaryFilter.gte = jobFilters.salary_min * 1000;
            }
            if (jobFilters.salary_max !== undefined) {
                salaryFilter.lte = jobFilters.salary_max * 1000;
            }
            if (Object.keys(salaryFilter).length > 0) {
                conditions.push({
                    entityDetailsJob: {
                        OR: [
                            { salaryMin: salaryFilter },
                            { salaryMax: salaryFilter }
                        ]
                    }
                });
            }
        }
        if (jobFilters.job_description) {
            conditions.push({
                entityDetailsJob: {
                    jobDescription: { contains: jobFilters.job_description, mode: 'insensitive' }
                }
            });
        }
        if (jobFilters.has_application_url !== undefined) {
            if (jobFilters.has_application_url) {
                conditions.push({
                    entityDetailsJob: {
                        applicationUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsJob: {
                        applicationUrl: null
                    }
                });
            }
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildHardwareFilters(hardwareFilters) {
        const conditions = [];
        if (hardwareFilters.gpu_search) {
            conditions.push({
                entityDetailsHardware: {
                    gpu: { contains: hardwareFilters.gpu_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.processor_search) {
            conditions.push({
                entityDetailsHardware: {
                    processor: { contains: hardwareFilters.processor_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.memory_search) {
            conditions.push({
                entityDetailsHardware: {
                    memory: { contains: hardwareFilters.memory_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.price_search) {
            conditions.push({
                entityDetailsHardware: {
                    price: { contains: hardwareFilters.price_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.storage_search) {
            conditions.push({
                entityDetailsHardware: {
                    storage: { contains: hardwareFilters.storage_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.availability_search) {
            conditions.push({
                entityDetailsHardware: {
                    availability: { contains: hardwareFilters.availability_search, mode: 'insensitive' }
                }
            });
        }
        if (hardwareFilters.power_consumption_search) {
            conditions.push({
                entityDetailsHardware: {
                    powerConsumption: { contains: hardwareFilters.power_consumption_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildEventFilters(eventFilters) {
        const conditions = [];
        if (eventFilters.event_types?.length) {
            conditions.push({
                entityDetailsEvent: {
                    eventType: { in: eventFilters.event_types }
                }
            });
        }
        if (eventFilters.start_date_from || eventFilters.start_date_to) {
            const dateFilter = {};
            if (eventFilters.start_date_from)
                dateFilter.gte = new Date(eventFilters.start_date_from);
            if (eventFilters.start_date_to)
                dateFilter.lte = new Date(eventFilters.start_date_to);
            conditions.push({
                entityDetailsEvent: {
                    startDate: dateFilter
                }
            });
        }
        if (eventFilters.end_date_from || eventFilters.end_date_to) {
            const dateFilter = {};
            if (eventFilters.end_date_from)
                dateFilter.gte = new Date(eventFilters.end_date_from);
            if (eventFilters.end_date_to)
                dateFilter.lte = new Date(eventFilters.end_date_to);
            conditions.push({
                entityDetailsEvent: {
                    endDate: dateFilter
                }
            });
        }
        if (eventFilters.is_online !== undefined) {
            conditions.push({
                entityDetailsEvent: {
                    isOnline: eventFilters.is_online
                }
            });
        }
        if (eventFilters.location) {
            conditions.push({
                entityDetailsEvent: {
                    location: { contains: eventFilters.location, mode: 'insensitive' }
                }
            });
        }
        if (eventFilters.has_registration_url !== undefined) {
            if (eventFilters.has_registration_url) {
                conditions.push({
                    entityDetailsEvent: {
                        registrationUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsEvent: {
                        registrationUrl: null
                    }
                });
            }
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildToolFilters(toolFilters) {
        const conditions = [];
        if (toolFilters.technical_levels?.length) {
            conditions.push({
                entityDetailsTool: {
                    technicalLevel: { in: toolFilters.technical_levels }
                }
            });
        }
        if (toolFilters.learning_curves?.length) {
            conditions.push({
                entityDetailsTool: {
                    learningCurve: { in: toolFilters.learning_curves }
                }
            });
        }
        if (toolFilters.pricing_models?.length) {
            conditions.push({
                entityDetailsTool: {
                    pricingModel: { in: toolFilters.pricing_models }
                }
            });
        }
        if (toolFilters.price_ranges?.length) {
            conditions.push({
                entityDetailsTool: {
                    priceRange: { in: toolFilters.price_ranges }
                }
            });
        }
        if (toolFilters.has_api !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    apiAccess: toolFilters.has_api
                }
            });
        }
        if (toolFilters.has_free_tier !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    hasFreeTier: toolFilters.has_free_tier
                }
            });
        }
        if (toolFilters.open_source !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    openSource: toolFilters.open_source
                }
            });
        }
        if (toolFilters.mobile_support !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    mobileSupport: toolFilters.mobile_support
                }
            });
        }
        if (toolFilters.demo_available !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    demoAvailable: toolFilters.demo_available
                }
            });
        }
        if (toolFilters.has_live_chat !== undefined) {
            conditions.push({
                entityDetailsTool: {
                    hasLiveChat: toolFilters.has_live_chat
                }
            });
        }
        if (toolFilters.platforms?.length) {
            conditions.push({
                entityDetailsTool: {
                    platforms: { array_contains: toolFilters.platforms }
                }
            });
        }
        if (toolFilters.integrations?.length) {
            conditions.push({
                entityDetailsTool: {
                    integrations: { array_contains: toolFilters.integrations }
                }
            });
        }
        if (toolFilters.frameworks?.length) {
            conditions.push({
                entityDetailsTool: {
                    frameworks: { array_contains: toolFilters.frameworks }
                }
            });
        }
        if (toolFilters.libraries?.length) {
            conditions.push({
                entityDetailsTool: {
                    libraries: { array_contains: toolFilters.libraries }
                }
            });
        }
        if (toolFilters.deployment_options?.length) {
            conditions.push({
                entityDetailsTool: {
                    deploymentOptions: { array_contains: toolFilters.deployment_options }
                }
            });
        }
        if (toolFilters.support_channels?.length) {
            conditions.push({
                entityDetailsTool: {
                    supportChannels: { array_contains: toolFilters.support_channels }
                }
            });
        }
        if (toolFilters.key_features_search) {
            conditions.push({
                entityDetailsTool: {
                    keyFeatures: { array_contains: [toolFilters.key_features_search] }
                }
            });
        }
        if (toolFilters.use_cases_search) {
            conditions.push({
                entityDetailsTool: {
                    useCases: { array_contains: [toolFilters.use_cases_search] }
                }
            });
        }
        if (toolFilters.target_audience_search) {
            conditions.push({
                entityDetailsTool: {
                    targetAudience: { array_contains: [toolFilters.target_audience_search] }
                }
            });
        }
        if (toolFilters.customization_level) {
            conditions.push({
                entityDetailsTool: {
                    customizationLevel: { contains: toolFilters.customization_level, mode: 'insensitive' }
                }
            });
        }
        if (toolFilters.pricing_details_search) {
            conditions.push({
                entityDetailsTool: {
                    pricingDetails: { contains: toolFilters.pricing_details_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildAgencyFilters(agencyFilters) {
        const conditions = [];
        if (agencyFilters.services_offered?.length) {
            conditions.push({
                entityDetailsAgency: {
                    servicesOffered: { array_contains: agencyFilters.services_offered }
                }
            });
        }
        if (agencyFilters.industry_focus?.length) {
            conditions.push({
                entityDetailsAgency: {
                    industryFocus: { array_contains: agencyFilters.industry_focus }
                }
            });
        }
        if (agencyFilters.target_client_size?.length) {
            conditions.push({
                entityDetailsAgency: {
                    targetClientSize: { array_contains: agencyFilters.target_client_size }
                }
            });
        }
        if (agencyFilters.target_audience?.length) {
            conditions.push({
                entityDetailsAgency: {
                    targetAudience: { array_contains: agencyFilters.target_audience }
                }
            });
        }
        if (agencyFilters.location_summary) {
            conditions.push({
                entityDetailsAgency: {
                    locationSummary: { contains: agencyFilters.location_summary, mode: 'insensitive' }
                }
            });
        }
        if (agencyFilters.has_portfolio !== undefined) {
            if (agencyFilters.has_portfolio) {
                conditions.push({
                    entityDetailsAgency: {
                        portfolioUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsAgency: {
                        portfolioUrl: null
                    }
                });
            }
        }
        if (agencyFilters.pricing_info_search) {
            conditions.push({
                entityDetailsAgency: {
                    pricingInfo: { contains: agencyFilters.pricing_info_search, mode: 'insensitive' }
                }
            });
        }
        if (agencyFilters.services_search) {
            conditions.push({
                entityDetailsAgency: {
                    servicesOffered: { array_contains: [agencyFilters.services_search] }
                }
            });
        }
        if (agencyFilters.industry_search) {
            conditions.push({
                entityDetailsAgency: {
                    industryFocus: { array_contains: [agencyFilters.industry_search] }
                }
            });
        }
        if (agencyFilters.audience_search) {
            conditions.push({
                entityDetailsAgency: {
                    targetAudience: { array_contains: [agencyFilters.audience_search] }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildSoftwareFilters(softwareFilters) {
        const conditions = [];
        if (softwareFilters.license_types?.length) {
            conditions.push({
                entityDetailsSoftware: {
                    licenseType: { in: softwareFilters.license_types }
                }
            });
        }
        if (softwareFilters.programming_languages?.length) {
            conditions.push({
                entityDetailsSoftware: {
                    programmingLanguages: { array_contains: softwareFilters.programming_languages }
                }
            });
        }
        if (softwareFilters.platform_compatibility?.length) {
            conditions.push({
                entityDetailsSoftware: {
                    platformCompatibility: { array_contains: softwareFilters.platform_compatibility }
                }
            });
        }
        if (softwareFilters.open_source !== undefined) {
            conditions.push({
                entityDetailsSoftware: {
                    openSource: softwareFilters.open_source
                }
            });
        }
        if (softwareFilters.has_repository !== undefined) {
            if (softwareFilters.has_repository) {
                conditions.push({
                    entityDetailsSoftware: {
                        repositoryUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsSoftware: {
                        repositoryUrl: null
                    }
                });
            }
        }
        if (softwareFilters.current_version) {
            conditions.push({
                entityDetailsSoftware: {
                    currentVersion: { contains: softwareFilters.current_version, mode: 'insensitive' }
                }
            });
        }
        if (softwareFilters.release_date_from || softwareFilters.release_date_to) {
            const dateFilter = {};
            if (softwareFilters.release_date_from)
                dateFilter.gte = new Date(softwareFilters.release_date_from);
            if (softwareFilters.release_date_to)
                dateFilter.lte = new Date(softwareFilters.release_date_to);
            conditions.push({
                entityDetailsSoftware: {
                    releaseDate: dateFilter
                }
            });
        }
        if (softwareFilters.languages_search) {
            conditions.push({
                entityDetailsSoftware: {
                    programmingLanguages: { array_contains: [softwareFilters.languages_search] }
                }
            });
        }
        if (softwareFilters.platforms_search) {
            conditions.push({
                entityDetailsSoftware: {
                    platformCompatibility: { array_contains: [softwareFilters.platforms_search] }
                }
            });
        }
        if (softwareFilters.license_search) {
            conditions.push({
                entityDetailsSoftware: {
                    licenseType: { contains: softwareFilters.license_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildResearchPaperFilters(researchPaperFilters) {
        const conditions = [];
        if (researchPaperFilters.authors?.length) {
            conditions.push({
                entityDetailsResearchPaper: {
                    authors: { array_contains: researchPaperFilters.authors }
                }
            });
        }
        if (researchPaperFilters.research_areas?.length) {
            conditions.push({
                entityDetailsResearchPaper: {
                    researchAreas: { array_contains: researchPaperFilters.research_areas }
                }
            });
        }
        if (researchPaperFilters.publication_venues?.length) {
            conditions.push({
                entityDetailsResearchPaper: {
                    publicationVenues: { array_contains: researchPaperFilters.publication_venues }
                }
            });
        }
        if (researchPaperFilters.keywords?.length) {
            conditions.push({
                entityDetailsResearchPaper: {
                    keywords: { array_contains: researchPaperFilters.keywords }
                }
            });
        }
        if (researchPaperFilters.doi) {
            conditions.push({
                entityDetailsResearchPaper: {
                    doi: { contains: researchPaperFilters.doi, mode: 'insensitive' }
                }
            });
        }
        if (researchPaperFilters.publication_date_from || researchPaperFilters.publication_date_to) {
            const dateFilter = {};
            if (researchPaperFilters.publication_date_from)
                dateFilter.gte = new Date(researchPaperFilters.publication_date_from);
            if (researchPaperFilters.publication_date_to)
                dateFilter.lte = new Date(researchPaperFilters.publication_date_to);
            conditions.push({
                entityDetailsResearchPaper: {
                    publicationDate: dateFilter
                }
            });
        }
        if (researchPaperFilters.citation_count_min !== undefined || researchPaperFilters.citation_count_max !== undefined) {
            const citationFilter = {};
            if (researchPaperFilters.citation_count_min !== undefined)
                citationFilter.gte = researchPaperFilters.citation_count_min;
            if (researchPaperFilters.citation_count_max !== undefined)
                citationFilter.lte = researchPaperFilters.citation_count_max;
            conditions.push({
                entityDetailsResearchPaper: {
                    citationCount: citationFilter
                }
            });
        }
        if (researchPaperFilters.abstract_search) {
            conditions.push({
                entityDetailsResearchPaper: {
                    abstract: { contains: researchPaperFilters.abstract_search, mode: 'insensitive' }
                }
            });
        }
        if (researchPaperFilters.authors_search) {
            conditions.push({
                entityDetailsResearchPaper: {
                    authors: { array_contains: [researchPaperFilters.authors_search] }
                }
            });
        }
        if (researchPaperFilters.research_areas_search) {
            conditions.push({
                entityDetailsResearchPaper: {
                    researchAreas: { array_contains: [researchPaperFilters.research_areas_search] }
                }
            });
        }
        if (researchPaperFilters.venues_search) {
            conditions.push({
                entityDetailsResearchPaper: {
                    publicationVenues: { array_contains: [researchPaperFilters.venues_search] }
                }
            });
        }
        if (researchPaperFilters.keywords_search) {
            conditions.push({
                entityDetailsResearchPaper: {
                    keywords: { array_contains: [researchPaperFilters.keywords_search] }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildPodcastFilters(podcastFilters) {
        const conditions = [];
        if (podcastFilters.host) {
            conditions.push({
                entityDetailsPodcast: {
                    host: { contains: podcastFilters.host, mode: 'insensitive' }
                }
            });
        }
        if (podcastFilters.main_topics?.length) {
            conditions.push({
                entityDetailsPodcast: {
                    mainTopics: { hasSome: podcastFilters.main_topics }
                }
            });
        }
        if (podcastFilters.frequency?.length) {
            conditions.push({
                entityDetailsPodcast: {
                    frequency: { in: podcastFilters.frequency }
                }
            });
        }
        if (podcastFilters.average_length) {
            conditions.push({
                entityDetailsPodcast: {
                    averageLength: { contains: podcastFilters.average_length, mode: 'insensitive' }
                }
            });
        }
        if (podcastFilters.has_spotify !== undefined) {
            if (podcastFilters.has_spotify) {
                conditions.push({
                    entityDetailsPodcast: {
                        spotifyUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsPodcast: {
                        spotifyUrl: null
                    }
                });
            }
        }
        if (podcastFilters.has_apple_podcasts !== undefined) {
            if (podcastFilters.has_apple_podcasts) {
                conditions.push({
                    entityDetailsPodcast: {
                        applePodcastsUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsPodcast: {
                        applePodcastsUrl: null
                    }
                });
            }
        }
        if (podcastFilters.has_google_podcasts !== undefined) {
            if (podcastFilters.has_google_podcasts) {
                conditions.push({
                    entityDetailsPodcast: {
                        googlePodcastsUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsPodcast: {
                        googlePodcastsUrl: null
                    }
                });
            }
        }
        if (podcastFilters.has_youtube !== undefined) {
            if (podcastFilters.has_youtube) {
                conditions.push({
                    entityDetailsPodcast: {
                        youtubeUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsPodcast: {
                        youtubeUrl: null
                    }
                });
            }
        }
        if (podcastFilters.topics_search) {
            conditions.push({
                entityDetailsPodcast: {
                    mainTopics: { has: podcastFilters.topics_search }
                }
            });
        }
        if (podcastFilters.frequency_search) {
            conditions.push({
                entityDetailsPodcast: {
                    frequency: { contains: podcastFilters.frequency_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildCommunityFilters(communityFilters) {
        const conditions = [];
        if (communityFilters.community_types?.length) {
            conditions.push({
                entityDetailsCommunity: {
                    communityType: { in: communityFilters.community_types }
                }
            });
        }
        if (communityFilters.focus_areas?.length) {
            conditions.push({
                entityDetailsCommunity: {
                    focusAreas: { array_contains: communityFilters.focus_areas }
                }
            });
        }
        if (communityFilters.target_audience?.length) {
            conditions.push({
                entityDetailsCommunity: {
                    targetAudience: { array_contains: communityFilters.target_audience }
                }
            });
        }
        if (communityFilters.member_count_min !== undefined || communityFilters.member_count_max !== undefined) {
            const memberCountFilter = {};
            if (communityFilters.member_count_min !== undefined)
                memberCountFilter.gte = communityFilters.member_count_min;
            if (communityFilters.member_count_max !== undefined)
                memberCountFilter.lte = communityFilters.member_count_max;
            conditions.push({
                entityDetailsCommunity: {
                    memberCount: memberCountFilter
                }
            });
        }
        if (communityFilters.is_free !== undefined) {
            conditions.push({
                entityDetailsCommunity: {
                    isFree: communityFilters.is_free
                }
            });
        }
        if (communityFilters.requires_invitation !== undefined) {
            conditions.push({
                entityDetailsCommunity: {
                    requiresInvitation: communityFilters.requires_invitation
                }
            });
        }
        if (communityFilters.has_events !== undefined) {
            conditions.push({
                entityDetailsCommunity: {
                    hasEvents: communityFilters.has_events
                }
            });
        }
        if (communityFilters.focus_areas_search) {
            conditions.push({
                entityDetailsCommunity: {
                    focusAreas: { array_contains: [communityFilters.focus_areas_search] }
                }
            });
        }
        if (communityFilters.audience_search) {
            conditions.push({
                entityDetailsCommunity: {
                    targetAudience: { array_contains: [communityFilters.audience_search] }
                }
            });
        }
        if (communityFilters.type_search) {
            conditions.push({
                entityDetailsCommunity: {
                    communityType: { contains: communityFilters.type_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildGrantFilters(grantFilters) {
        const conditions = [];
        if (grantFilters.funding_organizations?.length) {
            conditions.push({
                entityDetailsGrant: {
                    fundingOrganization: { in: grantFilters.funding_organizations }
                }
            });
        }
        if (grantFilters.research_areas?.length) {
            conditions.push({
                entityDetailsGrant: {
                    researchAreas: { array_contains: grantFilters.research_areas }
                }
            });
        }
        if (grantFilters.eligible_applicants?.length) {
            conditions.push({
                entityDetailsGrant: {
                    eligibleApplicants: { array_contains: grantFilters.eligible_applicants }
                }
            });
        }
        if (grantFilters.funding_amount_min !== undefined || grantFilters.funding_amount_max !== undefined) {
            const fundingFilter = {};
            if (grantFilters.funding_amount_min !== undefined)
                fundingFilter.gte = grantFilters.funding_amount_min * 1000;
            if (grantFilters.funding_amount_max !== undefined)
                fundingFilter.lte = grantFilters.funding_amount_max * 1000;
            conditions.push({
                entityDetailsGrant: {
                    fundingAmount: fundingFilter
                }
            });
        }
        if (grantFilters.deadline_from || grantFilters.deadline_to) {
            const dateFilter = {};
            if (grantFilters.deadline_from)
                dateFilter.gte = new Date(grantFilters.deadline_from);
            if (grantFilters.deadline_to)
                dateFilter.lte = new Date(grantFilters.deadline_to);
            conditions.push({
                entityDetailsGrant: {
                    applicationDeadline: dateFilter
                }
            });
        }
        if (grantFilters.is_open !== undefined) {
            conditions.push({
                entityDetailsGrant: {
                    isOpen: grantFilters.is_open
                }
            });
        }
        if (grantFilters.supports_international !== undefined) {
            conditions.push({
                entityDetailsGrant: {
                    supportsInternational: grantFilters.supports_international
                }
            });
        }
        if (grantFilters.research_areas_search) {
            conditions.push({
                entityDetailsGrant: {
                    researchAreas: { array_contains: [grantFilters.research_areas_search] }
                }
            });
        }
        if (grantFilters.organization_search) {
            conditions.push({
                entityDetailsGrant: {
                    fundingOrganization: { contains: grantFilters.organization_search, mode: 'insensitive' }
                }
            });
        }
        if (grantFilters.applicants_search) {
            conditions.push({
                entityDetailsGrant: {
                    eligibleApplicants: { array_contains: [grantFilters.applicants_search] }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildNewsletterFilters(newsletterFilters) {
        const conditions = [];
        if (newsletterFilters.frequency?.length) {
            conditions.push({
                entityDetailsNewsletter: {
                    frequency: { in: newsletterFilters.frequency }
                }
            });
        }
        if (newsletterFilters.focus_areas?.length) {
            conditions.push({
                entityDetailsNewsletter: {
                    focusAreas: { array_contains: newsletterFilters.focus_areas }
                }
            });
        }
        if (newsletterFilters.target_audience?.length) {
            conditions.push({
                entityDetailsNewsletter: {
                    targetAudience: { array_contains: newsletterFilters.target_audience }
                }
            });
        }
        if (newsletterFilters.subscriber_count_min !== undefined || newsletterFilters.subscriber_count_max !== undefined) {
            const subscriberFilter = {};
            if (newsletterFilters.subscriber_count_min !== undefined)
                subscriberFilter.gte = newsletterFilters.subscriber_count_min;
            if (newsletterFilters.subscriber_count_max !== undefined)
                subscriberFilter.lte = newsletterFilters.subscriber_count_max;
            conditions.push({
                entityDetailsNewsletter: {
                    subscriberCount: subscriberFilter
                }
            });
        }
        if (newsletterFilters.is_free !== undefined) {
            conditions.push({
                entityDetailsNewsletter: {
                    isFree: newsletterFilters.is_free
                }
            });
        }
        if (newsletterFilters.has_archives !== undefined) {
            conditions.push({
                entityDetailsNewsletter: {
                    hasArchives: newsletterFilters.has_archives
                }
            });
        }
        if (newsletterFilters.author) {
            conditions.push({
                entityDetailsNewsletter: {
                    author: { contains: newsletterFilters.author, mode: 'insensitive' }
                }
            });
        }
        if (newsletterFilters.focus_areas_search) {
            conditions.push({
                entityDetailsNewsletter: {
                    focusAreas: { array_contains: [newsletterFilters.focus_areas_search] }
                }
            });
        }
        if (newsletterFilters.audience_search) {
            conditions.push({
                entityDetailsNewsletter: {
                    targetAudience: { array_contains: [newsletterFilters.audience_search] }
                }
            });
        }
        if (newsletterFilters.frequency_search) {
            conditions.push({
                entityDetailsNewsletter: {
                    frequency: { contains: newsletterFilters.frequency_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildBookFilters(bookFilters) {
        const conditions = [];
        if (bookFilters.author) {
            conditions.push({
                entityDetailsBook: {
                    author: { contains: bookFilters.author, mode: 'insensitive' }
                }
            });
        }
        if (bookFilters.publisher) {
            conditions.push({
                entityDetailsBook: {
                    publisher: { contains: bookFilters.publisher, mode: 'insensitive' }
                }
            });
        }
        if (bookFilters.isbn) {
            conditions.push({
                entityDetailsBook: {
                    isbn: { contains: bookFilters.isbn, mode: 'insensitive' }
                }
            });
        }
        if (bookFilters.formats?.length) {
            conditions.push({
                entityDetailsBook: {
                    format: { in: bookFilters.formats }
                }
            });
        }
        if (bookFilters.publication_date_from || bookFilters.publication_date_to) {
            const dateFilter = {};
            if (bookFilters.publication_date_from)
                dateFilter.gte = new Date(bookFilters.publication_date_from);
            if (bookFilters.publication_date_to)
                dateFilter.lte = new Date(bookFilters.publication_date_to);
            conditions.push({
                entityDetailsBook: {
                    publicationDate: dateFilter
                }
            });
        }
        if (bookFilters.page_count_min !== undefined || bookFilters.page_count_max !== undefined) {
            const pageCountFilter = {};
            if (bookFilters.page_count_min !== undefined)
                pageCountFilter.gte = bookFilters.page_count_min;
            if (bookFilters.page_count_max !== undefined)
                pageCountFilter.lte = bookFilters.page_count_max;
            conditions.push({
                entityDetailsBook: {
                    pageCount: pageCountFilter
                }
            });
        }
        if (bookFilters.has_purchase_url !== undefined) {
            if (bookFilters.has_purchase_url) {
                conditions.push({
                    entityDetailsBook: {
                        purchaseUrl: { not: null }
                    }
                });
            }
            else {
                conditions.push({
                    entityDetailsBook: {
                        purchaseUrl: null
                    }
                });
            }
        }
        if (bookFilters.summary_search) {
            conditions.push({
                entityDetailsBook: {
                    summary: { contains: bookFilters.summary_search, mode: 'insensitive' }
                }
            });
        }
        if (bookFilters.format_search) {
            conditions.push({
                entityDetailsBook: {
                    format: { contains: bookFilters.format_search, mode: 'insensitive' }
                }
            });
        }
        return conditions.length > 0 ? { AND: conditions } : null;
    }
    buildOrderBy(sortBy, sortOrder) {
        switch (sortBy) {
            case 'averageRating':
                return {
                    reviews: {
                        _avg: {
                            rating: sortOrder
                        }
                    }
                };
            case 'reviewCount':
                return {
                    reviews: {
                        _count: sortOrder
                    }
                };
            case 'saveCount':
                return {
                    userSavedEntities: {
                        _count: sortOrder
                    }
                };
            case 'popularity':
                return {
                    userSavedEntities: {
                        _count: sortOrder
                    }
                };
            case 'relevance':
                return { createdAt: sortOrder };
            case 'name':
                return { name: sortOrder };
            case 'foundedYear':
                return { foundedYear: sortOrder };
            case 'updatedAt':
                return { updatedAt: sortOrder };
            case 'createdAt':
            default:
                return { createdAt: sortOrder };
        }
    }
};
exports.EntitiesService = EntitiesService;
exports.EntitiesService = EntitiesService = EntitiesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        openai_service_1.OpenaiService,
        activity_logger_service_1.ActivityLoggerService,
        validation_service_1.ValidationService])
], EntitiesService);
//# sourceMappingURL=entities.service.js.map