"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const entity_type_response_dto_1 = require("../../entity-types/dto/entity-type-response.dto");
const user_profile_minimal_dto_1 = require("../../auth/dto/user-profile-minimal.dto");
const category_response_dto_1 = require("../../categories/dto/category-response.dto");
const tag_response_dto_1 = require("../../tags/dto/tag-response.dto");
const _generated_prisma_1 = require("@generated-prisma");
const review_response_dto_1 = require("../../reviews/dto/review-response.dto");
const feature_response_dto_1 = require("../../features/dto/feature-response.dto");
class EntityResponseDto {
}
exports.EntityResponseDto = EntityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID)', example: '123e4567-e89b-12d3-a456-************' }),
    __metadata("design:type", String)
], EntityResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the entity', example: 'Super AI Tool' }),
    __metadata("design:type", String)
], EntityResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL-friendly slug for the entity', example: 'super-ai-tool' }),
    __metadata("design:type", String)
], EntityResponseDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Website URL of the entity', example: 'https://superaitool.com' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "websiteUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of the entity', type: () => entity_type_response_dto_1.EntityTypeResponseDto }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Short description of the entity', example: 'A tool that revolutionizes AI development.' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "shortDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Full description of the entity', example: 'Super AI Tool offers a comprehensive suite of features...' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL of the entity's logo", example: 'https://superaitool.com/logo.png' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "logoUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL to the entity's documentation", example: 'https://docs.superaitool.com' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "documentationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL for contacting the entity', example: 'https://superaitool.com/contact' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "contactUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL to the entity's privacy policy", example: 'https://superaitool.com/privacy' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "privacyPolicyUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Year the entity was founded', example: 2022, type: 'integer' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "foundedYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: _generated_prisma_1.EntityStatus, description: 'Current status of the entity', example: _generated_prisma_1.EntityStatus.ACTIVE }),
    __metadata("design:type", String)
], EntityResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Social media links for the entity',
        type: 'object',
        additionalProperties: { type: 'string' },
        example: { twitter: 'https://twitter.com/superaitool', linkedIn: 'https://linkedin.com/company/superaitool' },
    }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "socialLinks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'User who submitted the entity', type: () => user_profile_minimal_dto_1.UserProfileMinimalDto }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "submitter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Legacy ID if applicable', example: 'old-system-id-123' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "legacyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of reviews for the entity', example: 42, default: 0, type: 'integer' }),
    __metadata("design:type", Number)
], EntityResponseDto.prototype, "reviewCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average rating for the entity', example: 4.5, default: 0, type: 'number', format: 'float' }),
    __metadata("design:type", Number)
], EntityResponseDto.prototype, "avgRating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the entity was created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], EntityResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the entity', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], EntityResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Meta title for SEO purposes', example: 'Super AI Tool | The Best AI Tool' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Meta description for SEO purposes', example: 'Discover Super AI Tool, the leading platform for AI development and innovation.' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Label for the scraped review sentiment', example: 'Positive' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "scrapedReviewSentimentLabel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for the scraped review sentiment', example: 0.95, type: 'number', format: 'float' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "scrapedReviewSentimentScore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Count of scraped reviews', example: 120, type: 'integer' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "scrapedReviewCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Range of employee count for the company',
        enum: _generated_prisma_1.EmployeeCountRange,
        example: _generated_prisma_1.EmployeeCountRange.C51_200,
    }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "employeeCountRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Funding stage of the company',
        enum: _generated_prisma_1.FundingStage,
        example: _generated_prisma_1.FundingStage.SERIES_B,
    }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "fundingStage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Summary of the company location', example: 'San Francisco, CA' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "locationSummary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Affiliate referral link', example: 'https://superaitool.com/?ref=123' }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "refLink", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: _generated_prisma_1.AffiliateStatus, description: 'Affiliate status of the entity', example: _generated_prisma_1.AffiliateStatus.APPROVED }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "affiliateStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of users who have saved this entity', example: 123, default: 0, type: 'integer' }),
    __metadata("design:type", Number)
], EntityResponseDto.prototype, "saveCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type-specific details of the entity. The structure depends on the entityType.',
        type: 'object',
        additionalProperties: true,
        example: { keyFeatures: ['Feature A', 'Feature B'], hasApi: true },
    }),
    __metadata("design:type", Object)
], EntityResponseDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Categories associated with the entity', type: () => [category_response_dto_1.CategoryResponseDto] }),
    __metadata("design:type", Array)
], EntityResponseDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tags associated with the entity', type: () => [tag_response_dto_1.TagResponseDto] }),
    __metadata("design:type", Array)
], EntityResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: () => [feature_response_dto_1.FeatureResponseDto] }),
    __metadata("design:type", Array)
], EntityResponseDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: () => [review_response_dto_1.ReviewResponseDto] }),
    __metadata("design:type", Array)
], EntityResponseDto.prototype, "reviews", void 0);
//# sourceMappingURL=entity-response.dto.js.map