"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const _generated_prisma_1 = require("@generated-prisma");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class ToolFiltersDto {
}
exports.ToolFiltersDto = ToolFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by technical level required to use the tool',
        enum: _generated_prisma_1.TechnicalLevel,
        isArray: true,
        example: [_generated_prisma_1.TechnicalLevel.BEGINNER, _generated_prisma_1.TechnicalLevel.INTERMEDIATE],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.TechnicalLevel, { each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "technical_levels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by learning curve difficulty',
        enum: _generated_prisma_1.LearningCurve,
        isArray: true,
        example: [_generated_prisma_1.LearningCurve.LOW, _generated_prisma_1.LearningCurve.MEDIUM],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.LearningCurve, { each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "learning_curves", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by pricing model',
        enum: _generated_prisma_1.PricingModel,
        isArray: true,
        example: [_generated_prisma_1.PricingModel.FREE, _generated_prisma_1.PricingModel.FREEMIUM],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PricingModel, { each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "pricing_models", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by price range',
        enum: _generated_prisma_1.PriceRange,
        isArray: true,
        example: [_generated_prisma_1.PriceRange.FREE, _generated_prisma_1.PriceRange.LOW],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PriceRange, { each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "price_ranges", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have API access',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "has_api", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have a free tier',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that are open source',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "open_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have mobile support',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "mobile_support", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have demo available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "demo_available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by supported platforms',
        type: [String],
        example: ['Web', 'iOS', 'Android', 'Desktop'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by integrations (searches within the JSON array)',
        type: [String],
        example: ['Slack', 'Discord', 'Zapier'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by frameworks supported',
        type: [String],
        example: ['TensorFlow', 'PyTorch', 'Hugging Face'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "frameworks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by libraries supported',
        type: [String],
        example: ['OpenAI', 'Anthropic', 'Cohere'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "libraries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in key features (searches within the JSON array)',
        example: 'natural language processing',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolFiltersDto.prototype, "key_features_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in use cases (searches within the JSON array)',
        example: 'content generation',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolFiltersDto.prototype, "use_cases_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in target audience (searches within the JSON array)',
        example: 'developers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolFiltersDto.prototype, "target_audience_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by deployment options',
        type: [String],
        example: ['Cloud', 'On-premise', 'Hybrid'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "deployment_options", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by support channels available',
        type: [String],
        example: ['Email', 'Chat', 'Phone', 'Community'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolFiltersDto.prototype, "support_channels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have live chat support',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ToolFiltersDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by customization level',
        example: 'high',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolFiltersDto.prototype, "customization_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in pricing details (partial match)',
        example: 'per user',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolFiltersDto.prototype, "pricing_details_search", void 0);
//# sourceMappingURL=tool-filters.dto.js.map